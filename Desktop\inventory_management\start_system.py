#!/usr/bin/env python
"""
Quick Start Script for Inventory Management System
This script will check the system and start the development server.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and return success status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False

def main():
    print("🚀 INVENTORY MANAGEMENT SYSTEM - QUICK START")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("manage.py").exists():
        print("❌ Error: manage.py not found. Please run this script from the project directory.")
        return False
    
    # Check if virtual environment is activated
    if not os.environ.get('VIRTUAL_ENV'):
        print("⚠️  Virtual environment not detected.")
        print("   Recommended: Activate virtual environment first")
        print("   Windows: .\\invent_venv\\Scripts\\activate")
        print("   Linux/Mac: source invent_venv/bin/activate")
        print()
    
    # Run system checks
    print("🔍 Running system checks...")
    
    # Check Django installation
    if not run_command("python -c \"import django; print('Django', django.get_version())\"", 
                      "Checking Django installation"):
        print("💡 Install Django: pip install django")
        return False
    
    # Run Django system check
    if not run_command("python manage.py check", "Django system check"):
        return False
    
    # Check if database is migrated
    if not run_command("python manage.py showmigrations --plan", "Checking migrations"):
        print("💡 Run migrations: python manage.py migrate")
        return False
    
    # Check if admin user exists
    admin_check = subprocess.run(
        "python manage.py shell -c \"from django.contrib.auth.models import User; print('admin_exists:', User.objects.filter(username='admin').exists())\"",
        shell=True, capture_output=True, text=True
    )
    
    if "admin_exists: False" in admin_check.stdout:
        print("👤 Admin user not found. Creating admin user...")
        if not run_command("python manage.py reset_admin", "Creating admin user"):
            return False
    else:
        print("✅ Admin user exists")
    
    # Check if demo data exists
    demo_check = subprocess.run(
        "python manage.py shell -c \"from inventory.models import Product; print('products_count:', Product.objects.count())\"",
        shell=True, capture_output=True, text=True
    )
    
    if "products_count: 0" in demo_check.stdout:
        print("📊 No demo data found. Setting up demo data...")
        if not run_command("python manage.py setup_demo_data", "Setting up demo data"):
            print("⚠️  Demo data setup failed, but system can still run")
    else:
        print("✅ Demo data exists")
    
    print("\n" + "=" * 50)
    print("🎉 SYSTEM READY!")
    print("\n📋 System Information:")
    print("   - Admin Username: admin")
    print("   - Admin Password: admin123")
    print("   - Server URL: http://localhost:8000")
    print("\n🔧 Useful Commands:")
    print("   - Reset admin password: python manage.py reset_admin")
    print("   - Add demo data: python manage.py setup_demo_data")
    print("   - Backup data: python manage.py backup_data")
    print("   - Run tests: python manage.py test inventory")
    print("   - System health check: python check_system.py")
    
    # Ask if user wants to start the server
    print("\n🚀 Start the development server? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '']:
            print("\n🌐 Starting development server...")
            print("   Press Ctrl+C to stop the server")
            print("   Open your browser to: http://localhost:8000")
            print("-" * 50)
            
            # Start the server
            os.system("python manage.py runserver")
        else:
            print("\n✅ System is ready. Start manually with: python manage.py runserver")
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please check the system manually.")
