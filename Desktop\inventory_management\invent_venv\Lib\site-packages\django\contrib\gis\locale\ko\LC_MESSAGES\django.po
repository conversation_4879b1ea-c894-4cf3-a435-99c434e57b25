# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, Ha <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2017
# minsung kang, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2023-12-04 18:45+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Korean (http://app.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "GIS"
msgstr "GIS"

msgid "The base GIS field."
msgstr "기본 GIS 필드"

msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr "기본 지리 정보 필드 — 오픈GIS에 특화된 지리 정보 형식의 맵"

msgid "Point"
msgstr "위치"

msgid "Line string"
msgstr "한줄 문자열"

msgid "Polygon"
msgstr "다각형"

msgid "Multi-point"
msgstr "다중 위치"

msgid "Multi-line string"
msgstr "여러줄 문자열"

msgid "Multi polygon"
msgstr "복수 다각형"

msgid "Geometry collection"
msgstr "지리적 위치 모음"

msgid "Extent Aggregate Field"
msgstr "확장 집계 필드"

msgid "Raster Field"
msgstr "래스터 필드"

msgid "No geometry value provided."
msgstr "지리 값이 없습니다."

msgid "Invalid geometry value."
msgstr "잘못된 지리 값."

msgid "Invalid geometry type."
msgstr "잘못된 지리 형식."

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr "Geometry를 geometry 필드의 SRID로 변환하는 도중 오류가 발생하였습니다."

msgid "Delete all Features"
msgstr "모든 기능 삭제"

msgid "Debugging window (serialized value)"
msgstr "디버깅 창 (연속된 값)"

msgid "No feeds are registered."
msgstr "등록된 피드가 없습니다."

#, python-format
msgid "Slug %r isn’t registered."
msgstr "%r 이/가 등록되지 않았습니다."
